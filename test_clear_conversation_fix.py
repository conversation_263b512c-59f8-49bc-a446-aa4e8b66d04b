#!/usr/bin/env python3
"""
Teste para verificar se a função search_osf ainda funciona após limpar a conversa.
"""

import sys
import os

# Adicionar diretório atual ao path
sys.path.insert(0, '.')

def test_search_before_clear():
    """Testa busca antes de limpar a conversa."""
    print("🔍 TESTE 1: Busca ANTES de limpar a conversa")
    print("-" * 50)
    
    try:
        from gemini_mcp_server import gemini_server
        
        # Testar busca
        result = gemini_server.send_chat_message("Buscar projetos sobre machine learning")
        
        print("✅ Busca executada antes de limpar")
        print(f"📊 Resultado ({len(result)} caracteres):")
        print(result[:200] + "..." if len(result) > 200 else result)
        
        return True
        
    except Exception as e:
        print(f"❌ Erro: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_clear_conversation():
    """Testa a função de limpar conversa."""
    print("\n🧹 TESTE 2: Limpando a conversa")
    print("-" * 50)
    
    try:
        from gemini_mcp_server import gemini_server
        
        # Limpar conversa
        gemini_server.clear_conversation()
        
        print("✅ Conversa limpa com sucesso")
        print(f"📊 Histórico: {len(gemini_server.conversation_history)} mensagens")
        print(f"🤖 Chat session: {'Ativo' if gemini_server.chat_session else 'Inativo'}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_search_after_clear():
    """Testa busca depois de limpar a conversa."""
    print("\n🔍 TESTE 3: Busca DEPOIS de limpar a conversa")
    print("-" * 50)
    
    try:
        from gemini_mcp_server import gemini_server
        
        # Testar busca novamente
        result = gemini_server.send_chat_message("Buscar projetos sobre psychology")
        
        print("✅ Busca executada depois de limpar")
        print(f"📊 Resultado ({len(result)} caracteres):")
        print(result[:200] + "..." if len(result) > 200 else result)
        
        # Verificar se a função search_osf foi chamada
        if "search_osf" in result.lower() or "projetos" in result.lower() or "osf.io" in result.lower():
            print("🎯 Função search_osf parece ter sido executada!")
            return True
        else:
            print("⚠️ Função search_osf pode não ter sido executada")
            return False
        
    except Exception as e:
        print(f"❌ Erro: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_direct_function_call():
    """Testa chamada direta da função search_osf."""
    print("\n🔧 TESTE 4: Chamada direta da função search_osf")
    print("-" * 50)
    
    try:
        from gemini_mcp_server import gemini_server
        
        # Chamar função diretamente
        result = gemini_server.search_osf_with_gemini("neuroscience", 3)
        
        print("✅ Função search_osf_with_gemini executada diretamente")
        print(f"📊 Resultado ({len(result)} caracteres):")
        print(result[:300] + "..." if len(result) > 300 else result)
        
        return True
        
    except Exception as e:
        print(f"❌ Erro: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_function_execution():
    """Testa execução de função pelo Gemini."""
    print("\n⚙️ TESTE 5: Execução de função pelo Gemini")
    print("-" * 50)
    
    try:
        from gemini_mcp_server import gemini_server
        
        # Simular chamada de função pelo Gemini
        result = gemini_server.execute_function("search_osf", {"query": "data analysis", "max_results": 3})
        
        print("✅ Função executada via execute_function")
        print(f"📊 Resultado ({len(result)} caracteres):")
        print(result[:300] + "..." if len(result) > 300 else result)
        
        return True
        
    except Exception as e:
        print(f"❌ Erro: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🧪 TESTE DE CORREÇÃO: SEARCH_OSF APÓS LIMPAR CONVERSA")
    print("=" * 70)
    
    tests = [
        ("Busca antes de limpar", test_search_before_clear),
        ("Limpar conversa", test_clear_conversation),
        ("Busca depois de limpar", test_search_after_clear),
        ("Chamada direta", test_direct_function_call),
        ("Execução de função", test_function_execution)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            success = test_func()
            results.append((test_name, success))
        except Exception as e:
            print(f"❌ Erro crítico em {test_name}: {e}")
            results.append((test_name, False))
    
    print(f"\n{'='*70}")
    print("📊 RESUMO DOS TESTES")
    print("="*70)
    
    for test_name, success in results:
        status = "✅ PASSOU" if success else "❌ FALHOU"
        print(f"{test_name:25} {status}")
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    
    print(f"\n🎯 Resultado: {passed}/{total} testes passaram")
    
    if results[2][1]:  # Se o teste "Busca depois de limpar" passou
        print("🎉 CORREÇÃO FUNCIONOU! A função search_osf funciona após limpar a conversa.")
        print("\n💡 Agora você pode:")
        print("   1. Usar a interface web normalmente")
        print("   2. Limpar a conversa quando quiser")
        print("   3. Continuar usando as funções de busca e download")
    else:
        print("⚠️ A correção pode não ter funcionado completamente.")
        print("   Verifique os erros acima para mais detalhes.")
