#!/usr/bin/env python3
"""
Teste da função de download corrigida.
"""

import sys
import os

# Adicionar diretório atual ao path
sys.path.insert(0, '.')

def test_download_function():
    """Testa a função de download corrigida."""
    print("🧪 TESTE DA FUNÇÃO DE DOWNLOAD CORRIGIDA")
    print("=" * 60)
    
    try:
        from gemini_mcp_server import GeminiOSFMCPServer
        print("✅ Servidor importado com sucesso")
        
        server = GeminiOSFMCPServer()
        print("✅ Servidor criado com sucesso")
        
        # Testar projetos conhecidos
        test_projects = [
            'https://osf.io/j4bv6/',  # Projeto com arquivos
            'https://osf.io/2zfu4/',  # Projeto com arquivos
            'https://osf.io/xyz123/' # Projeto inexistente (para testar)
        ]
        
        for project_url in test_projects:
            print(f"\n{'='*20} Testando {project_url} {'='*20}")
            
            try:
                result = server.download_real_osf_files(project_url, f'test_download_{project_url.split("/")[-2]}')
                
                if result:
                    print(f"✅ Download bem-sucedido: {len(result)} arquivos")
                    for file_info in result:
                        name = file_info.get('name', 'unknown')
                        status = file_info.get('status', 'unknown')
                        local_path = file_info.get('local_path', 'N/A')
                        print(f"   📄 {name}: {status}")
                        if os.path.exists(local_path):
                            size = os.path.getsize(local_path)
                            print(f"      Arquivo: {local_path} ({size} bytes)")
                        else:
                            print(f"      ⚠️ Arquivo não encontrado: {local_path}")
                else:
                    print(f"ℹ️ Projeto não tem arquivos disponíveis")
                    
            except Exception as e:
                print(f"❌ Erro: {e}")
                import traceback
                traceback.print_exc()
        
        print(f"\n{'='*60}")
        print("🎯 TESTE CONCLUÍDO")
        print("✅ Se você viu arquivos sendo baixados, a função está funcionando!")
        print("ℹ️ Se alguns projetos não têm arquivos, isso é normal.")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro crítico: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_download_with_gemini():
    """Testa a função completa com Gemini."""
    print("\n🤖 TESTE DA FUNÇÃO COMPLETA COM GEMINI")
    print("=" * 60)
    
    try:
        from gemini_mcp_server import gemini_server
        
        # Testar download com análise
        result = gemini_server.download_osf_files_with_gemini('https://osf.io/j4bv6/', 'test_gemini_download')
        
        print("✅ Função completa executada")
        print(f"📊 Resultado ({len(result)} caracteres):")
        print("-" * 50)
        print(result[:500] + "..." if len(result) > 500 else result)
        print("-" * 50)
        
        return True
        
    except Exception as e:
        print(f"❌ Erro: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 INICIANDO TESTES DE DOWNLOAD")
    print("=" * 60)
    
    success1 = test_download_function()
    success2 = test_download_with_gemini()
    
    print(f"\n{'='*60}")
    print("📊 RESUMO DOS TESTES:")
    print(f"Download básico: {'✅ PASSOU' if success1 else '❌ FALHOU'}")
    print(f"Download + Gemini: {'✅ PASSOU' if success2 else '❌ FALHOU'}")
    
    if success1 or success2:
        print("\n🎉 Pelo menos um teste passou! A função de download foi corrigida.")
        print("\n💡 Agora você pode testar na interface web:")
        print("   1. Acesse: http://localhost:7862")
        print("   2. Digite: 'Download https://osf.io/j4bv6/'")
        print("   3. Veja os arquivos sendo baixados!")
    else:
        print("\n⚠️ Ambos os testes falharam. Verifique os erros acima.")
