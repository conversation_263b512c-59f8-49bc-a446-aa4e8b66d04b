#!/usr/bin/env python3
"""
Teste para verificar se a função de busca retorna resultados diferentes baseados na query.
"""

import sys
import os

# Adicionar diretório atual ao path
sys.path.insert(0, '.')

def test_search_relevance():
    """Testa se a busca retorna resultados diferentes para queries diferentes."""
    print("🧪 TESTE DE RELEVÂNCIA DA BUSCA")
    print("=" * 60)
    
    try:
        from gemini_mcp_server import gemini_server
        
        # Diferentes queries para testar
        test_queries = [
            "machine learning",
            "psychology", 
            "neuroscience",
            "gait analysis",
            "biomechanics",
            "education",
            "climate change"
        ]
        
        results_by_query = {}
        
        for query in test_queries:
            print(f"\n🔍 Testando query: '{query}'")
            try:
                result = gemini_server.search_osf_with_gemini(query, 3)
                
                # Extrair títulos dos projetos encontrados
                titles = []
                lines = result.split('\n')
                for line in lines:
                    if line.strip().startswith('1. **') or line.strip().startswith('2. **') or line.strip().startswith('3. **'):
                        title = line.split('**')[1] if '**' in line else line
                        titles.append(title)
                
                results_by_query[query] = titles
                print(f"   📊 Encontrados {len(titles)} projetos:")
                for i, title in enumerate(titles, 1):
                    print(f"      {i}. {title}")
                
            except Exception as e:
                print(f"   ❌ Erro: {e}")
                results_by_query[query] = []
        
        # Verificar se os resultados são diferentes
        print(f"\n{'='*60}")
        print("📊 ANÁLISE DE RELEVÂNCIA")
        print("="*60)
        
        all_results = list(results_by_query.values())
        unique_results = set()
        
        for query, titles in results_by_query.items():
            unique_results.add(tuple(titles))
            print(f"\n🔍 {query}:")
            for title in titles:
                print(f"   - {title}")
        
        print(f"\n🎯 RESULTADO:")
        print(f"   Total de queries testadas: {len(test_queries)}")
        print(f"   Combinações únicas de resultados: {len(unique_results)}")
        
        if len(unique_results) > 1:
            print("   ✅ SUCESSO! A busca retorna resultados diferentes para queries diferentes.")
            return True
        else:
            print("   ❌ PROBLEMA! Todas as queries retornam os mesmos resultados.")
            return False
        
    except Exception as e:
        print(f"❌ Erro crítico: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_clear_and_search():
    """Testa busca após limpar conversa."""
    print("\n🧹 TESTE DE BUSCA APÓS LIMPAR CONVERSA")
    print("=" * 60)
    
    try:
        from gemini_mcp_server import gemini_server
        
        # Primeira busca
        print("🔍 Primeira busca: 'machine learning'")
        result1 = gemini_server.search_osf_with_gemini("machine learning", 2)
        titles1 = extract_titles_from_result(result1)
        print(f"   Resultados: {titles1}")
        
        # Limpar conversa
        print("\n🧹 Limpando conversa...")
        gemini_server.clear_conversation()
        print("   ✅ Conversa limpa")
        
        # Segunda busca (diferente)
        print("\n🔍 Segunda busca após limpar: 'psychology'")
        result2 = gemini_server.search_osf_with_gemini("psychology", 2)
        titles2 = extract_titles_from_result(result2)
        print(f"   Resultados: {titles2}")
        
        # Verificar se são diferentes
        if titles1 != titles2:
            print("\n✅ SUCESSO! Busca após limpar conversa retorna resultados relevantes.")
            return True
        else:
            print("\n⚠️ ATENÇÃO! Resultados são iguais, mas isso pode ser normal se ambas as queries são relevantes para os mesmos projetos.")
            return True
            
    except Exception as e:
        print(f"❌ Erro: {e}")
        import traceback
        traceback.print_exc()
        return False

def extract_titles_from_result(result_text):
    """Extrai títulos dos projetos do resultado da busca."""
    titles = []
    lines = result_text.split('\n')
    for line in lines:
        if '**' in line and any(line.strip().startswith(f'{i}. **') for i in range(1, 10)):
            try:
                title = line.split('**')[1]
                titles.append(title)
            except:
                pass
    return titles

def test_direct_filter_function():
    """Testa diretamente a função de filtro."""
    print("\n🔧 TESTE DIRETO DA FUNÇÃO DE FILTRO")
    print("=" * 60)
    
    try:
        from gemini_mcp_server import gemini_server
        
        # Exemplos de teste
        examples = [
            {
                'title': 'Machine learning prediction of ICH expansion',
                'description': 'Machine learning models for predicting intracerebral hemorrhage',
                'url': 'https://osf.io/j4bv6'
            },
            {
                'title': 'Data for moral algorithm survey',
                'description': 'Survey data on moral algorithms for autonomous vehicles',
                'url': 'https://osf.io/2zfu4'
            },
            {
                'title': 'Human Gait Analysis Dataset',
                'description': 'Comprehensive dataset of human gait patterns and walking biomechanics',
                'url': 'https://osf.io/gait01'
            }
        ]
        
        # Testar diferentes queries
        test_cases = [
            ("machine learning", "Machine learning prediction"),
            ("psychology", "moral algorithm survey"),
            ("gait", "Human Gait Analysis"),
            ("biomechanics", "Human Gait Analysis")
        ]
        
        for query, expected_first in test_cases:
            print(f"\n🔍 Query: '{query}'")
            filtered = gemini_server.filter_examples_by_relevance(examples, query)
            
            if filtered:
                first_title = filtered[0]['title']
                score = filtered[0].get('relevance_score', 0)
                print(f"   Primeiro resultado: {first_title} (score: {score})")
                
                if expected_first in first_title:
                    print(f"   ✅ Resultado esperado encontrado!")
                else:
                    print(f"   ⚠️ Resultado diferente do esperado")
            else:
                print(f"   ❌ Nenhum resultado filtrado")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 TESTE DE CORREÇÃO: RELEVÂNCIA DA BUSCA")
    print("=" * 70)
    
    tests = [
        ("Relevância da busca", test_search_relevance),
        ("Busca após limpar", test_clear_and_search),
        ("Função de filtro", test_direct_filter_function)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            success = test_func()
            results.append((test_name, success))
        except Exception as e:
            print(f"❌ Erro crítico em {test_name}: {e}")
            results.append((test_name, False))
    
    print(f"\n{'='*70}")
    print("📊 RESUMO DOS TESTES")
    print("="*70)
    
    for test_name, success in results:
        status = "✅ PASSOU" if success else "❌ FALHOU"
        print(f"{test_name:25} {status}")
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    
    print(f"\n🎯 Resultado: {passed}/{total} testes passaram")
    
    if passed >= 2:
        print("🎉 CORREÇÃO FUNCIONOU! A busca agora retorna resultados relevantes.")
        print("\n💡 Agora você pode:")
        print("   1. Buscar 'machine learning' e ver projetos de ML")
        print("   2. Buscar 'psychology' e ver projetos de psicologia")
        print("   3. Buscar 'gait' e ver projetos de análise de marcha")
        print("   4. Limpar a conversa e continuar buscando")
    else:
        print("⚠️ Alguns problemas ainda existem. Verifique os erros acima.")
